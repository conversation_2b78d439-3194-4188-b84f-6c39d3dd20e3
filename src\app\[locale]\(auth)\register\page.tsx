'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { useTranslations, useLocale } from 'next-intl'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { createClient } from '@/lib/supabase/client'
import { getAuthErrorKey } from '@/lib/auth-errors'
import { Eye, EyeOff, Coffee } from 'lucide-react'

export default function RegisterPage() {
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    confirmPassword: '',
    firstName: '',
    lastName: '',
    phone: '',
  })
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState(false)
  const router = useRouter()
  const t = useTranslations('auth.register')
  const locale = useLocale()

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({
      ...prev,
      [e.target.name]: e.target.value
    }))
  }

  const handleRegister = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError('')

    // Validation
    if (formData.password !== formData.confirmPassword) {
      setError(t('errors.passwordMismatch'))
      setIsLoading(false)
      return
    }

    if (formData.password.length < 6) {
      setError(t('errors.passwordTooShort'))
      setIsLoading(false)
      return
    }

    try {
      const supabase = createClient()
      console.log('🔐 Starting registration process for:', formData.email)

      // Register user with Supabase Auth
      const { data, error: authError } = await supabase.auth.signUp({
        email: formData.email,
        password: formData.password,
        options: {
          data: {
            first_name: formData.firstName,
            last_name: formData.lastName,
            phone: formData.phone,
            preferred_language: locale, // Save current locale as preferred language
          }
        }
      })

      if (authError) {
        console.error('🔐 Auth registration error:', authError)
        const errorKey = getAuthErrorKey(authError.message, 'register')
        setError(t(errorKey))
        return
      }

      if (!data.user) {
        console.error('🔐 No user data returned from auth signup')
        setError(t('errors.generic'))
        return
      }

      console.log('🔐 Auth user created successfully:', data.user.id)
      console.log('👤 User profile created automatically by database trigger')

      // Link any existing guest orders to this new user account
      console.log('🛒 Checking for guest orders to link...')
      try {
        const { data: linkedOrders, error: linkError } = await supabase
          .from('orders')
          .update({ user_id: data.user.id })
          .eq('email', formData.email)
          .is('user_id', null)
          .select('id, order_number')

        if (linkError) {
          console.error('🛒 Error linking guest orders:', linkError)
          // Don't fail registration for order linking issues
          console.log('🛒 Registration successful despite order linking error')
        } else {
          const orderCount = linkedOrders?.length || 0
          console.log(`🛒 Successfully linked ${orderCount} guest orders to new user account`)
          if (orderCount > 0) {
            console.log('🛒 Linked orders:', linkedOrders?.map(o => o.order_number).join(', '))
          }
        }
      } catch (linkingError) {
        console.error('🛒 Exception in order linking process:', linkingError)
        // Don't fail registration for order linking issues
      }

      console.log('✅ Registration completed successfully')
      setSuccess(true)
      setTimeout(() => {
        router.push(`/${locale}/login`)
      }, 2000)

    } catch (error) {
      console.error('🔐 Unexpected error during registration:', error)
      setError(t('errors.generic'))
    } finally {
      setIsLoading(false)
    }
  }

  if (success) {
    return (
      <Card>
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            <Coffee className="h-12 w-12 text-green-600" />
          </div>
          <CardTitle className="text-2xl text-green-600">{t('success')}</CardTitle>
          <CardDescription>
            {t('successMessage')}
          </CardDescription>
        </CardHeader>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader className="text-center">
        <div className="flex justify-center mb-4">
          <Coffee className="h-12 w-12 text-primary" />
        </div>
        <CardTitle className="text-2xl">{t('title')}</CardTitle>
        <CardDescription>
          {t('subtitle')}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleRegister} className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <label htmlFor="firstName" className="text-sm font-medium">
                {t('firstName')} *
              </label>
              <Input
                id="firstName"
                name="firstName"
                type="text"
                value={formData.firstName}
                onChange={handleChange}
                placeholder={t('firstNamePlaceholder')}
                required
                disabled={isLoading}
              />
            </div>
            
            <div className="space-y-2">
              <label htmlFor="lastName" className="text-sm font-medium">
                {t('lastName')} *
              </label>
              <Input
                id="lastName"
                name="lastName"
                type="text"
                value={formData.lastName}
                onChange={handleChange}
                placeholder={t('lastNamePlaceholder')}
                required
                disabled={isLoading}
              />
            </div>
          </div>

          <div className="space-y-2">
            <label htmlFor="email" className="text-sm font-medium">
              {t('email')} *
            </label>
            <Input
              id="email"
              name="email"
              type="email"
              value={formData.email}
              onChange={handleChange}
              placeholder={t('emailPlaceholder')}
              required
              disabled={isLoading}
            />
          </div>

          <div className="space-y-2">
            <label htmlFor="phone" className="text-sm font-medium">
              {t('phone')}
            </label>
            <Input
              id="phone"
              name="phone"
              type="tel"
              value={formData.phone}
              onChange={handleChange}
              placeholder={t('phonePlaceholder')}
              disabled={isLoading}
            />
          </div>
          
          <div className="space-y-2">
            <label htmlFor="password" className="text-sm font-medium">
              {t('password')} *
            </label>
            <div className="relative">
              <Input
                id="password"
                name="password"
                type={showPassword ? 'text' : 'password'}
                value={formData.password}
                onChange={handleChange}
                placeholder={t('passwordPlaceholder')}
                required
                disabled={isLoading}
              />
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                onClick={() => setShowPassword(!showPassword)}
                disabled={isLoading}
              >
                {showPassword ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </Button>
            </div>
          </div>

          <div className="space-y-2">
            <label htmlFor="confirmPassword" className="text-sm font-medium">
              {t('confirmPassword')} *
            </label>
            <div className="relative">
              <Input
                id="confirmPassword"
                name="confirmPassword"
                type={showConfirmPassword ? 'text' : 'password'}
                value={formData.confirmPassword}
                onChange={handleChange}
                placeholder={t('confirmPasswordPlaceholder')}
                required
                disabled={isLoading}
              />
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                disabled={isLoading}
              >
                {showConfirmPassword ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </Button>
            </div>
          </div>

          {error && (
            <div className="text-sm text-red-600 bg-red-50 p-3 rounded-md">
              {error}
            </div>
          )}

          <Button type="submit" className="w-full" disabled={isLoading}>
            {isLoading ? '...' : t('submit')}
          </Button>
        </form>

        <div className="mt-6 text-center text-sm">
          <span className="text-muted-foreground">{t('hasAccount')} </span>
          <Link href={`/${locale}/login`} className="text-primary hover:underline">
            {t('signIn')}
          </Link>
        </div>
      </CardContent>
    </Card>
  )
}

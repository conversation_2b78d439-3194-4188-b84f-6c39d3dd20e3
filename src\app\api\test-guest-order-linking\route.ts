import { NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase/admin'

export async function POST() {
  console.log('🧪 Guest Order Linking Test: Starting test...');

  try {
    // Check if this is a development environment
    if (process.env.NODE_ENV !== 'development') {
      console.log('🧪 Guest Order Linking Test: Only available in development');
      return NextResponse.json(
        { error: 'Test endpoint only available in development environment' },
        { status: 403 }
      );
    }

    const testEmail = '<EMAIL>';
    const testUserId = '961f9116-e58d-4344-b016-68c7b14c5416';

    // Check for guest orders before linking
    const { data: guestOrdersBefore, error: guestOrdersError } = await supabaseAdmin
      .from('orders')
      .select('id, order_number, user_id, email')
      .eq('email', testEmail)
      .is('user_id', null);

    if (guestOrdersError) {
      console.error('🧪 Guest Order Linking Test: Error fetching guest orders:', guestOrdersError);
      return NextResponse.json(
        { error: 'Failed to fetch guest orders', details: guestOrdersError.message },
        { status: 500 }
      );
    }

    console.log('🧪 Guest Order Linking Test: Guest orders before linking:', guestOrdersBefore);

    // Simulate the login order linking logic
    const { data: linkedOrders, error: linkError } = await supabaseAdmin
      .from('orders')
      .update({ user_id: testUserId })
      .eq('email', testEmail)
      .is('user_id', null)
      .select('id, order_number');

    if (linkError) {
      console.error('🧪 Guest Order Linking Test: Error linking orders:', linkError);
      return NextResponse.json(
        { error: 'Failed to link guest orders', details: linkError.message },
        { status: 500 }
      );
    }

    console.log('🧪 Guest Order Linking Test: Linked orders:', linkedOrders);

    // Verify all orders are now linked to the user
    const { data: allOrdersAfter, error: allOrdersError } = await supabaseAdmin
      .from('orders')
      .select('id, order_number, user_id, email')
      .eq('email', testEmail)
      .order('created_at', { ascending: false });

    if (allOrdersError) {
      console.error('🧪 Guest Order Linking Test: Error fetching all orders:', allOrdersError);
      return NextResponse.json(
        { error: 'Failed to fetch orders after linking', details: allOrdersError.message },
        { status: 500 }
      );
    }

    console.log('🧪 Guest Order Linking Test: All orders after linking:', allOrdersAfter);

    // Check if all orders are now linked to the user
    const unlinkedOrders = allOrdersAfter.filter(order => order.user_id !== testUserId);
    const success = unlinkedOrders.length === 0 && linkedOrders.length > 0;

    return NextResponse.json({
      success,
      message: success 
        ? 'Guest order linking test passed successfully' 
        : 'Guest order linking test failed',
      results: {
        guestOrdersBeforeLinking: guestOrdersBefore.length,
        ordersLinked: linkedOrders.length,
        totalOrdersAfterLinking: allOrdersAfter.length,
        unlinkedOrdersRemaining: unlinkedOrders.length,
        allOrdersLinkedToUser: unlinkedOrders.length === 0
      },
      testDetails: {
        testEmail,
        testUserId,
        guestOrdersBefore: guestOrdersBefore.map(o => ({ id: o.id, order_number: o.order_number, user_id: o.user_id })),
        linkedOrders: linkedOrders.map(o => ({ id: o.id, order_number: o.order_number })),
        allOrdersAfter: allOrdersAfter.map(o => ({ id: o.id, order_number: o.order_number, user_id: o.user_id })),
        unlinkedOrders: unlinkedOrders.map(o => ({ id: o.id, order_number: o.order_number, user_id: o.user_id }))
      }
    });

  } catch (error) {
    console.error('🧪 Guest Order Linking Test: Unexpected error:', error);
    return NextResponse.json(
      { 
        error: 'Test failed with unexpected error',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}
